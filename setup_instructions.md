# 机器状态数据标注系统设置指南

## 1. 环境准备

### 安装依赖
```bash
pip install label-studio pandas sqlalchemy psycopg2-binary requests
```

### 启动 Label Studio
```bash
label-studio start
```
默认访问地址：http://localhost:8080

## 2. 项目配置步骤

### 2.1 创建新项目
1. 登录 Label Studio 界面
2. 点击 "Create Project"
3. 输入项目名称：机器状态标注
4. 选择 "Custom" 模板

### 2.2 配置标注界面
有两个配置文件可选：
- `machine_state_simple_config.xml` - 简化版本（推荐）
- `machine_state_labeling_config.xml` - 完整版本

将选择的配置文件内容复制到 "Labeling Interface" 配置区域

### 2.3 快速测试
可以先导入 `sample_machine_data.json` 测试配置是否正常工作

### 2.3 数据导入方式

#### 方式一：CSV 文件导入
1. 从数据库导出 CSV 格式数据
2. 在项目中点击 "Import" 
3. 上传 CSV 文件

#### 方式二：API 批量导入（推荐）
使用 `database_integration_example.py` 脚本：

```python
# 修改数据库连接配置
DB_CONNECTION = "your_database_connection_string"
LABEL_STUDIO_URL = "http://localhost:8080"
API_TOKEN = "your_api_token"  # 在 Account & Settings 中获取

# 运行脚本
python database_integration_example.py
```

## 3. 标注工作流程

### 3.1 标注界面说明
- **表格区域**：显示机器的各项参数数据
- **状态分类**：选择机器当前状态（正常/警告/严重/故障等）
- **异常类型**：当选择异常状态时，进一步细分异常类型
- **置信度**：标注员对判断的信心程度（1-5星）
- **备注**：补充说明异常现象

### 3.2 标注标准建议
- **正常**：所有参数在正常范围内
- **警告**：某些参数接近阈值但未超出
- **严重**：参数超出正常范围，需要关注
- **故障**：设备明显异常，需要立即处理
- **维护**：设备处于维护状态
- **启动/关闭**：设备启停过程中的状态

## 4. 数据导出与分析

### 4.1 导出标注结果
```python
# 使用 API 导出
annotations = importer.export_annotations(project_id)

# 或在界面中点击 "Export" 按钮
```

### 4.2 结果格式
导出的 JSON 格式包含：
- 原始数据
- 标注结果（机器状态、异常类型等）
- 标注员信息
- 时间戳

## 5. 高级功能

### 5.1 预标注（Pre-labeling）
如果已有部分规则或模型，可以设置预标注：
```python
# 在导入任务时添加预测结果
task_data["predictions"] = [{
    "result": [{
        "from_name": "machine_state",
        "to_name": "machine_data", 
        "type": "choices",
        "value": {"choices": ["normal"]}
    }]
}]
```

### 5.2 质量控制
- 设置多人标注同一数据
- 计算标注一致性
- 设置专家审核流程

### 5.3 实时数据流
可以设置定时任务，定期从数据库拉取新数据进行标注

## 6. 其他推荐工具对比

| 工具 | 优势 | 适用场景 |
|------|------|----------|
| **Label Studio** | 功能全面、易用、开源 | 通用标注，推荐 |
| **Prodigy** | 主动学习、高效 | 需要快速迭代模型 |
| **Labelbox** | 企业级功能 | 大规模团队协作 |
| **CVAT** | 专业视频标注 | 主要用于视觉数据 |
| **自定义工具** | 完全定制 | 特殊需求场景 |

## 7. 最佳实践建议

1. **数据采样**：不需要标注所有数据，可以采用分层采样
2. **标注指南**：制定详细的标注规范文档
3. **质量监控**：定期检查标注质量和一致性
4. **增量标注**：随着数据增长，持续进行标注
5. **模型集成**：标注完成后可训练机器学习模型进行自动预测
