"""
Label Studio 数据库集成示例
用于从数据库导入机器状态数据进行标注
"""

import pandas as pd
import json
import requests
from sqlalchemy import create_engine
from datetime import datetime, timedelta

class MachineDataImporter:
    def __init__(self, db_connection_string, label_studio_url, api_token):
        self.engine = create_engine(db_connection_string)
        self.ls_url = label_studio_url
        self.headers = {'Authorization': f'Token {api_token}'}
    
    def fetch_machine_data(self, start_time, end_time, machine_id=None):
        """从数据库获取机器数据"""
        query = """
        SELECT 
            timestamp,
            machine_id,
            temperature,
            pressure,
            vibration,
            speed,
            power_consumption,
            error_code
        FROM machine_data 
        WHERE timestamp BETWEEN %s AND %s
        """
        
        params = [start_time, end_time]
        if machine_id:
            query += " AND machine_id = %s"
            params.append(machine_id)
            
        query += " ORDER BY timestamp"
        
        return pd.read_sql(query, self.engine, params=params)
    
    def prepare_labeling_data(self, df):
        """将数据转换为 Label Studio 格式"""
        tasks = []

        for idx, row in df.iterrows():
            # 创建文本描述
            text_description = f"""机器 {row['machine_id']} 数据记录
时间: {row['timestamp']}
温度: {row['temperature']}°C
压力: {row['pressure']} Pa
振动: {row['vibration']}
转速: {row['speed']} RPM
功耗: {row['power_consumption']} W
错误代码: {row['error_code'] if pd.notna(row['error_code']) else '无'}"""

            # 将每行数据转换为标注任务
            task_data = {
                "data": {
                    "text": text_description,
                    "timestamp": str(row['timestamp']),
                    "machine_id": str(row['machine_id']),
                    "temperature": float(row['temperature']),
                    "pressure": float(row['pressure']),
                    "vibration": float(row['vibration']),
                    "speed": float(row['speed']),
                    "power_consumption": float(row['power_consumption']),
                    "error_code": str(row['error_code']) if pd.notna(row['error_code']) else "无"
                }
            }
            tasks.append(task_data)

        return tasks
    
    def create_project(self, project_name, labeling_config):
        """创建 Label Studio 项目"""
        project_data = {
            "title": project_name,
            "label_config": labeling_config,
            "description": "机器运行状态数据标注项目"
        }
        
        response = requests.post(
            f"{self.ls_url}/api/projects/",
            headers=self.headers,
            json=project_data
        )
        
        if response.status_code == 201:
            return response.json()['id']
        else:
            raise Exception(f"创建项目失败: {response.text}")
    
    def import_tasks(self, project_id, tasks):
        """导入标注任务"""
        response = requests.post(
            f"{self.ls_url}/api/projects/{project_id}/import",
            headers=self.headers,
            json=tasks
        )
        
        if response.status_code == 201:
            return response.json()
        else:
            raise Exception(f"导入任务失败: {response.text}")
    
    def export_annotations(self, project_id):
        """导出标注结果"""
        response = requests.get(
            f"{self.ls_url}/api/projects/{project_id}/export",
            headers=self.headers,
            params={"exportType": "JSON"}
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"导出失败: {response.text}")

# 使用示例
def main():
    # 配置参数
    DB_CONNECTION = "postgresql://user:password@localhost:5432/machine_db"
    LABEL_STUDIO_URL = "http://localhost:8080"
    API_TOKEN = "your_api_token_here"
    
    # 读取标注配置
    with open('machine_state_labeling_config.xml', 'r', encoding='utf-8') as f:
        labeling_config = f.read()
    
    # 初始化导入器
    importer = MachineDataImporter(DB_CONNECTION, LABEL_STUDIO_URL, API_TOKEN)
    
    # 获取最近一周的数据
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    # 从数据库获取数据
    df = importer.fetch_machine_data(start_time, end_time)
    print(f"获取到 {len(df)} 条记录")
    
    # 准备标注数据
    tasks = importer.prepare_labeling_data(df)
    
    # 创建项目
    project_id = importer.create_project("机器状态标注", labeling_config)
    print(f"创建项目 ID: {project_id}")
    
    # 导入任务
    result = importer.import_tasks(project_id, tasks)
    print(f"导入完成: {result}")

if __name__ == "__main__":
    main()
